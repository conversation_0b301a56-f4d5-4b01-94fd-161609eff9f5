import React, { useState, useEffect } from "react";
import {
  Table,
  Button,
  Space,
  Typography,
  Card,
  Modal,
  Popconfirm,
  Tag,
  Tooltip,
  Input,
  Row,
  Col,
  Spin,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  ReloadOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import { toast } from "react-toastify";
import {
  fetchAllPharmacies,
  deletePharmacy,
  searchPharmacies,
} from "../../api/pharmacies";

const { Title } = Typography;
const { Search } = Input;

function Pharmacies() {
  const [pharmacies, setPharmacies] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [selectedPharmacy, setSelectedPharmacy] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState(""); // 'view', 'create', 'edit'

  // Fetch pharmacies data
  const fetchPharmacies = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      const response = await fetchAllPharmacies({
        page,
        limit: pageSize,
      });
      setPharmacies(response.data);
      setPagination((prev) => ({
        ...prev,
        current: page,
        pageSize,
        total: response.total || response.count || response.data?.length || 0,
      }));
    } catch (error) {
           

      console.error("Error fetching pharmacies:", error);
      toast.error("Failed to fetch pharmacies");
    } finally {
      setLoading(false);
    }
  };

  // Search pharmacies
  const handleSearch = async (searchTerm) => {
    if (!searchTerm.trim()) {
      fetchPharmacies();
      return;
    }

    setSearchLoading(true);
    try {
      const response = await searchPharmacies(searchTerm);
      setPharmacies(response.data || response.pharmacies || response);
      setPagination((prev) => ({
        ...prev,
        current: 1,
        total: response.total || response.count || response.data?.length || 0,
      }));
    } catch (error) {
      console.error("Error searching pharmacies:", error);
      toast.error("Failed to search pharmacies");
    } finally {
      setSearchLoading(false);
    }
  };

  // Delete pharmacy
  const handleDelete = async (pharmacyId) => {
    try {
      await deletePharmacy(pharmacyId);
      toast.success("Pharmacy deleted successfully");
      fetchPharmacies(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error("Error deleting pharmacy:", error);
      toast.error("Failed to delete pharmacy");
    }
  };

  // Handle table pagination
  const handleTableChange = (paginationInfo) => {
    fetchPharmacies(paginationInfo.current, paginationInfo.pageSize);
  };

  // Modal handlers
  const openModal = (type, pharmacy = null) => {
    setModalType(type);
    setSelectedPharmacy(pharmacy);
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedPharmacy(null);
    setModalType("");
  };

  // Refresh data
  const handleRefresh = () => {
    fetchPharmacies(pagination.current, pagination.pageSize);
  };

  // Load data on component mount
  useEffect(() => {
    fetchPharmacies();
  }, []);

  // Table columns configuration
  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      sorter: true,
      render: (text) => (
        <span style={{ fontWeight: 500, color: "#1890ff" }}>{text}</span>
      ),
    },
    {
      title: "Location",
      dataIndex: "location",
      key: "location",
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <span>
            <EnvironmentOutlined style={{ marginRight: 4, color: "#52c41a" }} />
            {text}
          </span>
        </Tooltip>
      ),
    },
    {
      title: "Phone",
      dataIndex: "phone",
      key: "phone",
      render: (text) => (
        <span>
          <PhoneOutlined style={{ marginRight: 4, color: "#1890ff" }} />
          {text}
        </span>
      ),
    },
    {
      title: "Working Hours",
      key: "workingHours",
      render: (_, record) => (
        <span>
          <ClockCircleOutlined style={{ marginRight: 4, color: "#fa8c16" }} />
          {record.start_time} - {record.finish_time}
        </span>
      ),
    },
    {
      title: "Coordinates",
      key: "coordinates",
      render: (_, record) => (
        <div>
          <div style={{ fontSize: "12px", color: "#666" }}>
            Lat: {record.latitude}
          </div>
          <div style={{ fontSize: "12px", color: "#666" }}>
            Lng: {record.longitude}
          </div>
        </div>
      ),
    },
    {
      title: "Status",
      key: "status",
      render: (_, record) => {
        const now = new Date();
        const currentTime = now.getHours() * 60 + now.getMinutes();
        const [startHour, startMin] = record.start_time.split(":").map(Number);
        const [endHour, endMin] = record.finish_time.split(":").map(Number);
        const startTime = startHour * 60 + startMin;
        const endTime = endHour * 60 + endMin;

        const isOpen = currentTime >= startTime && currentTime <= endTime;

        return (
          <Tag color={isOpen ? "green" : "red"}>
            {isOpen ? "Open" : "Closed"}
          </Tag>
        );
      },
    },
    {
      title: "Actions",
      key: "actions",
      fixed: "right",
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="View Details">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => openModal("view", record)}
              style={{ color: "#1890ff" }}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => openModal("edit", record)}
              style={{ color: "#52c41a" }}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Popconfirm
              title="Are you sure you want to delete this pharmacy?"
              onConfirm={() => handleDelete(record.id)}
              okText="Yes"
              cancelText="No"
              okType="danger"
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                style={{ color: "#ff4d4f" }}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: "24px" }}>
      {/* Header Section */}
      <Card style={{ marginBottom: "24px" }}>
        <Row
          justify="space-between"
          align="middle"
          style={{ marginBottom: "16px" }}
        >
          <Col>
            <Title level={2} style={{ margin: 0, color: "#1890ff" }}>
              Pharmacies Management
            </Title>
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => openModal("create")}
                size="large"
              >
                Create New Pharmacy
              </Button>
            </Space>
          </Col>
        </Row>

        {/* Search and Actions */}
        <Row gutter={16} style={{ marginBottom: "16px" }}>
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="Search pharmacies by name..."
              allowClear
              enterButton={<SearchOutlined />}
              size="large"
              onSearch={handleSearch}
              loading={searchLoading}
            />
          </Col>
          <Col xs={24} sm={12} md={16} style={{ textAlign: "right" }}>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
              >
                Refresh
              </Button>
              <span style={{ color: "#666" }}>
                Total: {pagination.total} pharmacies
              </span>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Table Section */}
      <Card>
        <Spin spinning={loading}>
          <Table
            columns={columns}
            dataSource={pharmacies}
            rowKey="id"
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `${range[0]}-${range[1]} of ${total} pharmacies`,
            }}
            onChange={handleTableChange}
            scroll={{ x: 1200 }}
            size="middle"
          />
        </Spin>
      </Card>

      {/* Modal for View/Create/Edit */}
      <Modal
        title={
          modalType === "view"
            ? "Pharmacy Details"
            : modalType === "create"
            ? "Create New Pharmacy"
            : "Edit Pharmacy"
        }
        open={showModal}
        onCancel={closeModal}
        footer={null}
        width={800}
      >
        {modalType === "view" && selectedPharmacy && (
          <div style={{ padding: "16px 0" }}>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <strong>Name:</strong> {selectedPharmacy.name}
              </Col>
              <Col span={12}>
                <strong>Phone:</strong> {selectedPharmacy.phone}
              </Col>
              <Col span={24}>
                <strong>Location:</strong> {selectedPharmacy.location}
              </Col>
              <Col span={12}>
                <strong>Start Time:</strong> {selectedPharmacy.start_time}
              </Col>
              <Col span={12}>
                <strong>Finish Time:</strong> {selectedPharmacy.finish_time}
              </Col>
              <Col span={12}>
                <strong>Latitude:</strong> {selectedPharmacy.latitude}
              </Col>
              <Col span={12}>
                <strong>Longitude:</strong> {selectedPharmacy.longitude}
              </Col>
            </Row>
          </div>
        )}

        {(modalType === "create" || modalType === "edit") && (
          <div style={{ padding: "16px 0" }}>
            <p>Pharmacy form will be implemented here</p>
            {/* TODO: Implement pharmacy form component */}
          </div>
        )}
      </Modal>
    </div>
  );
}

export default Pharmacies;
