// Authentication utility functions
import { toast } from 'react-toastify';

/**
 * Check if user is authenticated
 * @returns {boolean} - True if user has valid tokens
 */
export const isAuthenticated = () => {
  const authToken = localStorage.getItem('authToken');
  const token = localStorage.getItem('token');
  return !!(authToken && token);
};

/**
 * Get current user data from localStorage
 * @returns {object|null} - User object or null if not found
 */
export const getCurrentUser = () => {
  try {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  } catch (error) {
    console.error('Error parsing user data:', error);
    return null;
  }
};

/**
 * Clear all authentication data from localStorage
 */
export const clearAuthData = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('authToken');
  localStorage.removeItem('user');
};

/**
 * Logout user and redirect to login page
 * @param {boolean} showMessage - Whether to show logout message
 */
export const logoutUser = (showMessage = true) => {
  clearAuthData();
  
  if (showMessage) {
    toast.success('Logged out successfully');
  }
  
  // Use window.location for full page reload to clear any app state
  window.location.href = '/login';
};

/**
 * Handle session expiration
 */
export const handleSessionExpired = () => {
  clearAuthData();
  toast.error('Session expired. Please login again.');
  window.location.href = '/login';
};

/**
 * Store authentication data
 * @param {string} token - JWT token
 * @param {object} user - User data object
 */
export const storeAuthData = (token, user = null) => {
  localStorage.setItem('authToken', token);
  localStorage.setItem('token', token);
  
  if (user) {
    localStorage.setItem('user', JSON.stringify(user));
  }
};

/**
 * Check if token exists and is not expired (basic check)
 * Note: This is a basic check. For production, you might want to validate JWT expiration
 * @returns {boolean} - True if token appears valid
 */
export const isTokenValid = () => {
  const token = localStorage.getItem('token');
  
  if (!token) {
    return false;
  }
  
  try {
    // Basic JWT structure check (header.payload.signature)
    const parts = token.split('.');
    if (parts.length !== 3) {
      return false;
    }
    
    // Decode payload to check expiration (if present)
    const payload = JSON.parse(atob(parts[1]));
    
    // Check if token has expiration and if it's expired
    if (payload.exp) {
      const currentTime = Math.floor(Date.now() / 1000);
      if (payload.exp < currentTime) {
        return false;
      }
    }
    
    return true;
  } catch (error) {
    console.error('Error validating token:', error);
    return false;
  }
};
