// pharmacies.js - API functions for pharmacy management
import api from "./config";

/**
 * Create a new pharmacy
 * @param {Object} pharmacyData - Pharmacy data object
 * @param {string} pharmacyData.name - Pharmacy name
 * @param {string} pharmacyData.location - Pharmacy location/address
 * @param {string} pharmacyData.start_time - Opening time (e.g., "08:00")
 * @param {string} pharmacyData.finish_time - Closing time (e.g., "22:00")
 * @param {string} pharmacyData.phone - Pharmacy phone number
 * @param {number} pharmacyData.latitude - Latitude coordinate
 * @param {number} pharmacyData.longitude - Longitude coordinate
 * @returns {Promise<Object>} - Created pharmacy data
 */
export const createPharmacy = async (pharmacyData) => {
  try {
    const response = await api.post("/Admin/add_Pharmacy", {
      name: pharmacyData.name,
      location: pharmacyData.location,
      start_time: pharmacyData.start_time,
      finish_time: pharmacyData.finish_time,
      phone: pharmacyData.phone,
      latitude: pharmacyData.latitude,
      longitude: pharmacyData.longitude,
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

/**
 * Update an existing pharmacy
 * @param {number|string} pharmacyId - Pharmacy ID
 * @param {Object} pharmacyData - Updated pharmacy data object
 * @param {string} pharmacyData.name - Pharmacy name
 * @param {string} pharmacyData.location - Pharmacy location/address
 * @param {string} pharmacyData.start_time - Opening time (e.g., "08:00")
 * @param {string} pharmacyData.finish_time - Closing time (e.g., "22:00")
 * @param {string} pharmacyData.phone - Pharmacy phone number
 * @param {number} pharmacyData.latitude - Latitude coordinate
 * @param {number} pharmacyData.longitude - Longitude coordinate
 * @returns {Promise<Object>} - Updated pharmacy data
 */
export const updatePharmacy = async (pharmacyId, pharmacyData) => {
  try {
    const response = await api.put(`/Admin/update_Pharmacy`, {
      id: pharmacyId,
      name: pharmacyData.name,
      location: pharmacyData.location,
      start_time: pharmacyData.start_time,
      finish_time: pharmacyData.finish_time,
      phone: pharmacyData.phone,
      latitude: pharmacyData.latitude,
      longitude: pharmacyData.longitude,
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

/**
 * Delete a pharmacy
 * @param {number|string} pharmacyId - Pharmacy ID to delete
 * @returns {Promise<Object>} - Deletion confirmation
 */
export const deletePharmacy = async (pharmacyId) => {
  try {
    const response = await api.delete(`/pharmacies/${pharmacyId}`);
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

/**
 * Fetch all pharmacies
 * @param {Object} params - Optional query parameters
 * @param {number} params.page - Page number for pagination
 * @param {number} params.limit - Number of items per page
 * @param {string} params.sort - Sort field
 * @param {string} params.order - Sort order (asc/desc)
 * @returns {Promise<Object>} - List of pharmacies with pagination info
 */
export const fetchAllPharmacies = async (params = {}) => {
  try {
    const response = await api.get("/pharmacies", { params });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

/**
 * Search pharmacies by name
 * @param {string} name - Pharmacy name to search for
 * @param {Object} params - Optional additional parameters
 * @param {number} params.page - Page number for pagination
 * @param {number} params.limit - Number of items per page
 * @returns {Promise<Object>} - Search results with matching pharmacies
 */
export const searchPharmacies = async (name, params = {}) => {
  try {
    const response = await api.post("/pharmacies/search", {
      name,
      ...params,
    });
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};
